import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>r, <PERSON>u, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'
import './App.css'

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [cartItems, setCartItems] = useState(0)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-lg sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-blue-600 flex items-center gap-2">
                <Dumbbell className="w-6 h-6" />
                Nutrition Store
              </h1>
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-6">
              <a href="#calculator" className="text-gray-700 hover:text-blue-600 transition-colors">
                TDEE Calculator
              </a>
              <a href="#products" className="text-gray-700 hover:text-blue-600 transition-colors">
                Products
              </a>
              <a href="#plans" className="text-gray-700 hover:text-blue-600 transition-colors">
                Workout Plans
              </a>
              <button className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors">
                <User className="w-5 h-5" />
                Account
              </button>
              <button className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <ShoppingCart className="w-5 h-5" />
                Cart ({cartItems})
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t">
            <div className="px-4 py-2 space-y-2">
              <a href="#calculator" className="block py-2 text-gray-700">TDEE Calculator</a>
              <a href="#products" className="block py-2 text-gray-700">Products</a>
              <a href="#plans" className="block py-2 text-gray-700">Workout Plans</a>
              <a href="#account" className="block py-2 text-gray-700">Account</a>
              <button className="w-full text-left py-2 text-gray-700">Cart ({cartItems})</button>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">Transform Your Fitness Journey</h2>
          <p className="text-xl mb-8">Calculate your TDEE, discover premium supplements, and access personalized workout plans</p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center gap-2">
              <Calculator className="w-5 h-5" />
              Calculate TDEE Free
            </button>
            <button className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
              Shop Supplements
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <h3 className="text-3xl font-bold text-center mb-12">Why Choose Our Platform?</h3>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calculator className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-xl font-semibold mb-2">Free TDEE Calculator</h4>
              <p className="text-gray-600">Calculate your Total Daily Energy Expenditure with our scientifically accurate calculator</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="w-8 h-8 text-green-600" />
              </div>
              <h4 className="text-xl font-semibold mb-2">Premium Supplements</h4>
              <p className="text-gray-600">High-quality, tested supplements to support your fitness and health goals</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Dumbbell className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-xl font-semibold mb-2">Workout Plans</h4>
              <p className="text-gray-600">Personalized workout plans designed by certified fitness professionals</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-100">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h3 className="text-3xl font-bold mb-4">Ready to Start Your Journey?</h3>
          <p className="text-xl text-gray-600 mb-8">Join thousands of people who have transformed their lives with our platform</p>
          <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-700 transition-colors">
            Get Started Today
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <p>&copy; 2024 Nutrition Store. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

export default App
