@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@theme {
  --font-family-sans: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;

  /* Dark Theme Palette */
  --color-primary-50: #f8fafc;
  --color-primary-100: #f1f5f9;
  --color-primary-200: #e2e8f0;
  --color-primary-300: #cbd5e1;
  --color-primary-400: #94a3b8;
  --color-primary-500: #64748b;
  --color-primary-600: #475569;
  --color-primary-700: #334155;
  --color-primary-800: #1e293b;
  --color-primary-900: #0f172a;

  /* Accent Colors */
  --color-accent-50: #fafafa;
  --color-accent-100: #f4f4f5;
  --color-accent-200: #e4e4e7;
  --color-accent-300: #d4d4d8;
  --color-accent-400: #a1a1aa;
  --color-accent-500: #71717a;
  --color-accent-600: #52525b;
  --color-accent-700: #3f3f46;
  --color-accent-800: #27272a;
  --color-accent-900: #18181b;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #000000;
  color: #ffffff;
  font-family: var(--font-family-sans);
}

* {
  box-sizing: border-box;
}

html, body, #root {
  width: 100vw;
  max-width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.container-full {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

/* Custom utilities */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

.font-black {
  font-weight: 900;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 3s ease-in-out infinite;
  animation-delay: 1.5s;
}
