{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "autoprefixer": "^10.4.21", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^6.2.4"}, "dependencies": {"@inertiajs/inertia": "^0.11.1", "@inertiajs/react": "^2.0.14", "@vitejs/plugin-react": "^4.6.0", "react": "^19.1.0", "react-dom": "^19.1.0"}}