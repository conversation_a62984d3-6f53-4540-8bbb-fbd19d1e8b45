import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { ShoppingCart, User, Menu, X, Du<PERSON><PERSON>, LogOut } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useCart } from '../../contexts/CartContext'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { user, logout } = useAuth()
  const { cartItems } = useCart()
  const navigate = useNavigate()

  const handleLogout = () => {
    logout()
    navigate('/')
  }

  const cartItemCount = cartItems.reduce((total, item) => total + item.quantity, 0)

  return (
    <nav className="bg-black border-b border-gray-800 sticky top-0 z-50">
      <div className="container mx-auto px-6 lg:px-12">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link to="/" className="text-xl font-bold text-white flex items-center gap-2">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <Dumbbell className="w-5 h-5 text-black" />
              </div>
              NutriStore
            </Link>
          </div>

          {/* Desktop Menu */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/products" className="text-gray-300 hover:text-white transition-colors font-medium">
              Products
            </Link>
            <Link to="/calculator" className="text-gray-300 hover:text-white transition-colors font-medium">
              Calculator
            </Link>
            <Link to="/plans" className="text-gray-300 hover:text-white transition-colors font-medium">
              Workout Plans
            </Link>

            {user ? (
              <div className="flex items-center space-x-4">
                <Link to="/dashboard" className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors font-medium">
                  <User className="w-5 h-5" />
                  {user.name}
                </Link>
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 text-gray-300 hover:text-red-400 transition-colors font-medium"
                >
                  <LogOut className="w-5 h-5" />
                  Logout
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link to="/login" className="text-gray-300 hover:text-white transition-colors font-medium">
                  Login
                </Link>
                <Link to="/register" className="bg-white text-black px-6 py-2.5 rounded-lg hover:bg-gray-200 transition-all duration-300 font-semibold">
                  Get Started
                </Link>
              </div>
            )}

            <Link to="/cart" className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors font-medium relative">
              <ShoppingCart className="w-6 h-6" />
              {cartItemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-white text-black text-xs font-bold px-2 py-1 rounded-full min-w-[20px] text-center">
                  {cartItemCount}
                </span>
              )}
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-white"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-black border-t border-gray-800">
          <div className="px-4 py-2 space-y-2">
            <Link to="/calculator" className="block py-2 text-white">Multivitamin</Link>
            <Link to="/products" className="block py-2 text-white">Protein</Link>
            <Link to="/plans" className="block py-2 text-white">Muscle</Link>
            <Link to="/plans" className="block py-2 text-white">Reviews</Link>
            {user ? (
              <>
                <Link to="/dashboard" className="block py-2 text-white">Dashboard</Link>
                <button onClick={handleLogout} className="block w-full text-left py-2 text-white">Logout</button>
              </>
            ) : (
              <>
                <Link to="/login" className="block py-2 text-white">Login</Link>
                <Link to="/register" className="block py-2 text-orange-500">SHOP NOW</Link>
              </>
            )}
            <Link to="/cart" className="block py-2 text-white">Cart ({cartItemCount})</Link>
          </div>
        </div>
      )}
    </nav>
  )
}

export default Header
