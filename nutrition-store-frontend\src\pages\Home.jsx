import { Link } from 'react-router-dom'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Shield, Award, Zap } from 'lucide-react'

const Home = () => {
  return (
    <div className="bg-black min-h-screen w-full">
      {/* Hero Section */}
      <section className="bg-black relative overflow-hidden min-h-screen flex items-center w-screen">
        <div className="w-screen px-8 lg:px-16 2xl:px-24 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center w-full">
            <div className="space-y-8">
              <h1 className="text-5xl lg:text-7xl font-bold leading-tight text-white">
                Transform Your<br />
                <span className="text-gray-400">FITNESS JOURNEY</span>
              </h1>
              <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                Discover premium nutrition supplements designed to fuel your workouts, enhance recovery, and help you achieve your fitness goals faster than ever before.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/products"
                  className="inline-flex items-center justify-center bg-white text-black px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-200 transition-all duration-300"
                >
                  Shop Products
                </Link>
                <Link
                  to="/calculator"
                  className="inline-flex items-center justify-center border-2 border-gray-600 text-gray-300 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-800 hover:text-white transition-all duration-300"
                >
                  Try Calculator
                </Link>
              </div>
            </div>
            <div className="relative flex justify-center items-center">
              {/* Subtle background elements */}
              <div className="absolute top-20 right-20 w-32 h-32 bg-gray-800 rounded-full opacity-20 blur-xl"></div>
              <div className="absolute bottom-20 left-20 w-24 h-24 bg-gray-700 rounded-full opacity-30 blur-lg"></div>

              {/* Hero Image/Illustration */}
              <div className="relative w-full max-w-lg">
                <div className="bg-gray-900 rounded-2xl shadow-2xl p-8 border border-gray-800">
                  <img
                    src="/api/placeholder/400/500"
                    alt="Nutrition Products"
                    className="w-full h-auto rounded-xl"
                  />
                </div>

                {/* Floating stats cards */}
                <div className="absolute -top-4 -left-4 bg-gray-800 rounded-xl shadow-lg p-4 border border-gray-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                    <span className="text-sm font-semibold text-gray-300">1000+ Products</span>
                  </div>
                </div>

                <div className="absolute -bottom-4 -right-4 bg-gray-800 rounded-xl shadow-lg p-4 border border-gray-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span className="text-sm font-semibold text-gray-300">Expert Guidance</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-900 w-screen">
        <div className="w-screen px-8 lg:px-16 2xl:px-24">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Why Choose NutriStore</h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              We're committed to providing you with the best nutrition solutions and expert guidance
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="group bg-gray-800 rounded-2xl p-8 text-center hover:bg-gray-700 transition-all duration-300 border border-gray-700">
              <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Star className="w-8 h-8 text-black" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Expert Nutrition Guidance</h3>
              <p className="text-gray-400 leading-relaxed">
                Get personalized nutrition advice from certified nutritionists and dietitians to optimize your supplement routine and meal planning.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="group bg-gray-800 rounded-2xl p-8 text-center hover:bg-gray-700 transition-all duration-300 border border-gray-700">
              <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Award className="w-8 h-8 text-black" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Premium Quality Products</h3>
              <p className="text-gray-400 leading-relaxed">
                Carefully curated selection of top-tier supplements from trusted brands, ensuring you get the best value for your investment.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="group bg-gray-800 rounded-2xl p-8 text-center hover:bg-gray-700 transition-all duration-300 border border-gray-700">
              <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Zap className="w-8 h-8 text-black" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Fast & Reliable Delivery</h3>
              <p className="text-gray-400 leading-relaxed">
                Quick shipping and secure packaging ensure your supplements arrive fresh and ready to support your fitness journey.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-20 bg-black w-screen">
        <div className="w-screen px-8 lg:px-16 2xl:px-24">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Featured Products</h2>
            <p className="text-xl text-gray-400">Discover our most popular nutrition supplements</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Product 1 */}
            <div className="group bg-gray-900 rounded-2xl p-6 hover:bg-gray-800 transition-all duration-300 border border-gray-800">
              <div className="mb-6 relative overflow-hidden rounded-xl bg-gray-800 p-6">
                <img
                  src="/api/placeholder/200/200"
                  alt="Creatine Monohydrate"
                  className="w-full h-48 object-contain mx-auto group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Creatine Monohydrate</h3>
              <div className="flex items-center justify-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-white text-white" />
                ))}
                <span className="text-gray-400 text-sm ml-2">(124)</span>
              </div>
              <p className="text-2xl font-bold text-white mb-6">$34.99</p>
              <button className="w-full bg-white text-black py-3 rounded-lg font-semibold hover:bg-gray-200 transition-all duration-300">
                Add to Cart
              </button>
            </div>

            {/* Product 2 */}
            <div className="group bg-gray-900 rounded-2xl p-6 hover:bg-gray-800 transition-all duration-300 border border-gray-800">
              <div className="mb-6 relative overflow-hidden rounded-xl bg-gray-800 p-6">
                <img
                  src="/api/placeholder/200/200"
                  alt="Whey Protein Isolate"
                  className="w-full h-48 object-contain mx-auto group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Whey Protein Isolate</h3>
              <div className="flex items-center justify-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-white text-white" />
                ))}
                <span className="text-gray-400 text-sm ml-2">(89)</span>
              </div>
              <p className="text-2xl font-bold text-white mb-6">$54.99</p>
              <button className="w-full bg-white text-black py-3 rounded-lg font-semibold hover:bg-gray-200 transition-all duration-300">
                Add to Cart
              </button>
            </div>

            {/* Product 3 */}
            <div className="group bg-gray-900 rounded-2xl p-6 hover:bg-gray-800 transition-all duration-300 border border-gray-800">
              <div className="mb-6 relative overflow-hidden rounded-xl bg-gray-800 p-6">
                <img
                  src="/api/placeholder/200/200"
                  alt="BCAA Shock Powder"
                  className="w-full h-48 object-contain mx-auto group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">BCAA Shock Powder</h3>
              <div className="flex items-center justify-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-white text-white" />
                ))}
                <span className="text-gray-400 text-sm ml-2">(156)</span>
              </div>
              <p className="text-2xl font-bold text-white mb-6">$38.99</p>
              <button className="w-full bg-white text-black py-3 rounded-lg font-semibold hover:bg-gray-200 transition-all duration-300">
                Add to Cart
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* App Section */}
      <section className="py-20 bg-gray-900 relative overflow-hidden w-screen">
        <div className="w-screen px-8 lg:px-16 2xl:px-24 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight text-white">
                Download Our<br />
                <span className="text-gray-400">Mobile App</span>
              </h2>
              <p className="text-xl text-gray-400 leading-relaxed">
                Track your nutrition, access exclusive deals, and get personalized workout plans right from your phone.
              </p>
              <div className="flex space-x-4">
                <div className="bg-white rounded-xl p-3 hover:scale-105 transition-transform cursor-pointer shadow-lg">
                  <img
                    src="/api/placeholder/150/50"
                    alt="Download on App Store"
                    className="h-12 w-auto"
                  />
                </div>
                <div className="bg-white rounded-xl p-3 hover:scale-105 transition-transform cursor-pointer shadow-lg">
                  <img
                    src="/api/placeholder/150/50"
                    alt="Get it on Google Play"
                    className="h-12 w-auto"
                  />
                </div>
              </div>
            </div>
            <div className="relative flex justify-center items-center">
              {/* Subtle background shapes */}
              <div className="absolute top-10 right-10 w-32 h-32 bg-gray-700 rounded-full opacity-20 blur-2xl"></div>
              <div className="absolute bottom-10 left-10 w-24 h-24 bg-gray-600 rounded-full opacity-30 blur-xl"></div>

              {/* Mobile phone mockup */}
              <div className="relative z-10 bg-gray-800 rounded-2xl shadow-2xl p-4 border border-gray-700">
                <img
                  src="/api/placeholder/300/600"
                  alt="Mobile App Screenshot"
                  className="w-64 mx-auto rounded-xl"
                />
              </div>

              {/* Floating stats */}
              <div className="absolute top-20 -left-4 bg-gray-800 rounded-xl shadow-lg p-4 border border-gray-700">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                  <span className="text-sm font-semibold text-gray-300">10k+ Downloads</span>
                </div>
              </div>

              <div className="absolute bottom-20 -right-4 bg-gray-800 rounded-xl shadow-lg p-4 border border-gray-700">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                  <span className="text-sm font-semibold text-gray-300">4.9★ Rating</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-black w-screen">
        <div className="w-screen px-8 lg:px-16 2xl:px-24">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white leading-tight mb-4">
              What Our Customers Say About Us
            </h2>
            <p className="text-xl text-gray-400">Real stories from real customers</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-gray-900 rounded-2xl p-8 hover:bg-gray-800 transition-all duration-300 border border-gray-800">
              <div className="flex mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-white text-white" />
                ))}
              </div>
              <p className="text-gray-300 leading-relaxed text-lg mb-6 italic">
                "This nutrition store has completely transformed my fitness journey. The quality of supplements is outstanding and the customer service is exceptional!"
              </p>
              <div className="flex items-center">
                <img
                  src="/api/placeholder/60/60"
                  alt="Customer Jessica"
                  className="w-12 h-12 rounded-full mr-4 object-cover border-2 border-gray-700"
                />
                <div>
                  <h4 className="font-semibold text-white">Jessica L.</h4>
                  <p className="text-sm text-gray-400">Fitness Enthusiast</p>
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-gray-900 rounded-2xl p-8 hover:bg-gray-800 transition-all duration-300 border border-gray-800">
              <div className="flex mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-white text-white" />
                ))}
              </div>
              <p className="text-gray-300 leading-relaxed text-lg mb-6 italic">
                "Best prices I've found anywhere! The nutrition calculator helped me plan my supplements perfectly. Highly recommend this store."
              </p>
              <div className="flex items-center">
                <img
                  src="/api/placeholder/60/60"
                  alt="Customer Marcus"
                  className="w-12 h-12 rounded-full mr-4 object-cover border-2 border-gray-700"
                />
                <div>
                  <h4 className="font-semibold text-white">Marcus R.</h4>
                  <p className="text-sm text-gray-400">Personal Trainer</p>
                </div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-gray-900 rounded-2xl p-8 hover:bg-gray-800 transition-all duration-300 border border-gray-800">
              <div className="flex mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-white text-white" />
                ))}
              </div>
              <p className="text-gray-300 leading-relaxed text-lg mb-6 italic">
                "Fast shipping and the workout plans are amazing! I've seen incredible results in just 3 months. This is my go-to nutrition store now."
              </p>
              <div className="flex items-center">
                <img
                  src="/api/placeholder/60/60"
                  alt="Customer Sarah"
                  className="w-12 h-12 rounded-full mr-4 object-cover border-2 border-gray-700"
                />
                <div>
                  <h4 className="font-semibold text-white">Sarah M.</h4>
                  <p className="text-sm text-gray-400">Athlete</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gray-900 relative overflow-hidden w-screen">
        <div className="w-screen px-8 lg:px-16 2xl:px-24 relative z-10">
          <div className="bg-gray-800 rounded-2xl p-12 lg:p-16 text-white relative overflow-hidden border border-gray-700">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

            <div className="relative z-10 text-center max-w-4xl mx-auto">
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight mb-6">
                Ready to Start Your<br />
                <span className="text-gray-400">Fitness Journey?</span>
              </h2>
              <p className="text-xl text-gray-300 leading-relaxed mb-8 max-w-2xl mx-auto">
                Browse our premium supplements, use our nutrition calculator, or explore personalized workout plans to achieve your fitness goals.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/products"
                  className="bg-white text-black px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-200 transition-all duration-300"
                >
                  Shop Products
                </Link>
                <Link
                  to="/calculator"
                  className="border-2 border-gray-600 text-gray-300 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-700 hover:text-white transition-all duration-300"
                >
                  Try Calculator
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
