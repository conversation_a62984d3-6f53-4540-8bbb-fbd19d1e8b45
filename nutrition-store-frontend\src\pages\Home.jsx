import { Link } from 'react-router-dom'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>R<PERSON>, Shield, Award, Zap } from 'lucide-react'

const Home = () => {
  return (
    <div className="bg-black text-white min-h-screen">
      {/* Hero Section */}
      <section className="bg-black relative overflow-hidden min-h-screen flex items-center">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <h1 className="text-5xl lg:text-7xl font-black leading-tight tracking-tight">
                Transform Your<br />
                <span className="text-orange-500">FITNESS JOURNEY</span>
              </h1>
              <p className="text-lg text-gray-300 leading-relaxed max-w-lg">
                Discover premium nutrition supplements designed to fuel your workouts, enhance recovery, and help you achieve your fitness goals faster than ever before.
              </p>
              <Link
                to="/products"
                className="inline-block bg-orange-500 text-white px-10 py-4 rounded-md font-bold text-lg hover:bg-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                SHOP NOW
              </Link>
            </div>
            <div className="relative flex justify-center items-center">
              {/* Glowing background effect */}
              <div className="absolute inset-0 bg-gradient-radial from-orange-500/20 via-orange-500/10 to-transparent rounded-full blur-3xl scale-150"></div>

              {/* Product arrangement */}
              <div className="relative w-full max-w-lg h-96">
                {/* Main product - center */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30">
                  <img
                    src="/api/placeholder/200/280"
                    alt="Creatine Monohydrate"
                    className="w-48 h-auto drop-shadow-2xl"
                  />
                </div>

                {/* Secondary product - left */}
                <div className="absolute top-1/4 left-0 transform -translate-x-1/4 z-20 rotate-12">
                  <img
                    src="/api/placeholder/180/250"
                    alt="Whey Protein"
                    className="w-40 h-auto drop-shadow-xl opacity-80"
                  />
                </div>

                {/* Third product - right */}
                <div className="absolute bottom-1/4 right-0 transform translate-x-1/4 z-20 -rotate-12">
                  <img
                    src="/api/placeholder/160/220"
                    alt="BCAA Powder"
                    className="w-36 h-auto drop-shadow-xl opacity-80"
                  />
                </div>

                {/* Floating elements */}
                <div className="absolute top-10 right-10 w-4 h-4 bg-orange-500 rounded-full animate-pulse"></div>
                <div className="absolute bottom-10 left-10 w-3 h-3 bg-orange-400 rounded-full animate-pulse delay-1000"></div>
                <div className="absolute top-1/3 right-1/4 w-2 h-2 bg-orange-300 rounded-full animate-pulse delay-500"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-white rounded-2xl p-8 text-center shadow-2xl transform hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <Star className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Expert Nutrition Guidance</h3>
              <p className="text-gray-600 leading-relaxed">
                Get personalized nutrition advice from certified nutritionists and dietitians to optimize your supplement routine and meal planning.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-white rounded-2xl p-8 text-center shadow-2xl transform hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Premium Quality Products</h3>
              <p className="text-gray-600 leading-relaxed">
                Carefully curated selection of top-tier supplements from trusted brands, ensuring you get the best value for your investment.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="bg-white rounded-2xl p-8 text-center shadow-2xl transform hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Fast & Reliable Delivery</h3>
              <p className="text-gray-600 leading-relaxed">
                Quick shipping and secure packaging ensure your supplements arrive fresh and ready to support your fitness journey.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">FEATURED PRODUCTS</h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Product 1 */}
            <div className="bg-white rounded-2xl p-8 text-center shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="mb-6">
                <img
                  src="/api/placeholder/200/280"
                  alt="Creatine Monohydrate"
                  className="w-40 h-56 object-contain mx-auto"
                />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Creatine Monohydrate</h3>
              <div className="flex items-center justify-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-orange-500 text-orange-500" />
                ))}
                <span className="text-gray-500 text-sm ml-2">(124)</span>
              </div>
              <p className="text-3xl font-bold text-gray-900 mb-6">$34.99</p>
              <button className="w-full bg-orange-500 text-white py-4 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg">
                ADD TO CART
              </button>
            </div>

            {/* Product 2 */}
            <div className="bg-white rounded-2xl p-8 text-center shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="mb-6">
                <img
                  src="/api/placeholder/200/280"
                  alt="Whey Protein Isolate"
                  className="w-40 h-56 object-contain mx-auto"
                />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Whey Protein Isolate</h3>
              <div className="flex items-center justify-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-orange-500 text-orange-500" />
                ))}
                <span className="text-gray-500 text-sm ml-2">(89)</span>
              </div>
              <p className="text-3xl font-bold text-gray-900 mb-6">$54.99</p>
              <button className="w-full bg-orange-500 text-white py-4 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg">
                ADD TO CART
              </button>
            </div>

            {/* Product 3 */}
            <div className="bg-white rounded-2xl p-8 text-center shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="mb-6">
                <img
                  src="/api/placeholder/200/280"
                  alt="BCAA Shock Powder"
                  className="w-40 h-56 object-contain mx-auto"
                />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">BCAA Shock Powder</h3>
              <div className="flex items-center justify-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-orange-500 text-orange-500" />
                ))}
                <span className="text-gray-500 text-sm ml-2">(156)</span>
              </div>
              <p className="text-3xl font-bold text-gray-900 mb-6">$38.99</p>
              <button className="w-full bg-orange-500 text-white py-4 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg">
                ADD TO CART
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* App Section */}
      <section className="py-20 bg-black text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight">
                DOWNLOAD OUR<br />
                <span className="text-orange-500">MOBILE APP</span>
              </h2>
              <p className="text-lg text-gray-300 leading-relaxed">
                Track your nutrition, access exclusive deals, and get personalized workout plans right from your phone.
              </p>
              <div className="flex space-x-4">
                <div className="bg-white rounded-lg p-2 hover:scale-105 transition-transform cursor-pointer">
                  <img
                    src="/api/placeholder/150/50"
                    alt="Download on App Store"
                    className="h-12 w-auto"
                  />
                </div>
                <div className="bg-white rounded-lg p-2 hover:scale-105 transition-transform cursor-pointer">
                  <img
                    src="/api/placeholder/150/50"
                    alt="Get it on Google Play"
                    className="h-12 w-auto"
                  />
                </div>
              </div>
            </div>
            <div className="relative flex justify-center items-center">
              {/* Glowing background effect */}
              <div className="absolute inset-0 bg-gradient-radial from-orange-500/20 via-orange-500/10 to-transparent rounded-full blur-3xl scale-150"></div>

              {/* Mobile phone mockup */}
              <div className="relative z-10">
                <img
                  src="/api/placeholder/300/600"
                  alt="Mobile App Screenshot"
                  className="w-64 mx-auto drop-shadow-2xl"
                />
              </div>

              {/* Floating supplement bottle */}
              <div className="absolute top-1/2 right-0 transform translate-x-1/4 -translate-y-1/2 z-20">
                <img
                  src="/api/placeholder/200/280"
                  alt="Supplement Bottle"
                  className="w-40 h-auto opacity-80 drop-shadow-xl"
                />
              </div>

              {/* Floating elements */}
              <div className="absolute top-10 left-10 w-4 h-4 bg-orange-500 rounded-full animate-pulse"></div>
              <div className="absolute bottom-10 right-10 w-3 h-3 bg-orange-400 rounded-full animate-pulse delay-1000"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 leading-tight">
              WHAT OUR<br />
              <span className="text-orange-500">CUSTOMERS</span><br />
              SAY ABOUT US
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center mb-6">
                <img
                  src="/api/placeholder/60/60"
                  alt="Customer Jessica"
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <h4 className="font-bold text-gray-900 text-lg">Jessica L.</h4>
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-orange-500 text-orange-500" />
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-gray-600 leading-relaxed text-lg">
                "This nutrition store has completely transformed my fitness journey. The quality of supplements is outstanding and the customer service is exceptional!"
              </p>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center mb-6">
                <img
                  src="/api/placeholder/60/60"
                  alt="Customer Marcus"
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <h4 className="font-bold text-gray-900 text-lg">Marcus R.</h4>
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-orange-500 text-orange-500" />
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-gray-600 leading-relaxed text-lg">
                "Best prices I've found anywhere! The nutrition calculator helped me plan my supplements perfectly. Highly recommend this store."
              </p>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center mb-6">
                <img
                  src="/api/placeholder/60/60"
                  alt="Customer Sarah"
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <h4 className="font-bold text-gray-900 text-lg">Sarah M.</h4>
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-orange-500 text-orange-500" />
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-gray-600 leading-relaxed text-lg">
                "Fast shipping and the workout plans are amazing! I've seen incredible results in just 3 months. This is my go-to nutrition store now."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-black text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="relative flex justify-center items-center">
              {/* Glowing background effect */}
              <div className="absolute inset-0 bg-gradient-radial from-orange-500/20 via-orange-500/10 to-transparent rounded-full blur-3xl scale-150"></div>

              {/* Product arrangement */}
              <div className="relative w-full max-w-lg h-96">
                {/* Main product - center */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30">
                  <img
                    src="/api/placeholder/200/280"
                    alt="Main Product"
                    className="w-48 h-auto drop-shadow-2xl"
                  />
                </div>

                {/* Secondary product - left */}
                <div className="absolute top-1/4 left-0 transform -translate-x-1/4 z-20 rotate-12">
                  <img
                    src="/api/placeholder/180/250"
                    alt="Secondary Product"
                    className="w-40 h-auto drop-shadow-xl opacity-80"
                  />
                </div>

                {/* Floating elements */}
                <div className="absolute top-10 right-10 w-4 h-4 bg-orange-500 rounded-full animate-pulse"></div>
                <div className="absolute bottom-10 left-10 w-3 h-3 bg-orange-400 rounded-full animate-pulse delay-1000"></div>
              </div>
            </div>
            <div className="space-y-8">
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight">
                READY TO START<br />
                <span className="text-orange-500">YOUR JOURNEY?</span>
              </h2>
              <p className="text-lg text-gray-300 leading-relaxed">
                Browse our premium supplements, use our nutrition calculator, or explore personalized workout plans to achieve your fitness goals.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/products"
                  className="bg-orange-500 text-white px-10 py-4 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg text-center"
                >
                  SHOP NOW
                </Link>
                <Link
                  to="/calculator"
                  className="border-2 border-orange-500 text-orange-500 px-10 py-4 rounded-xl font-bold text-lg hover:bg-orange-500 hover:text-white transition-all duration-300 text-center"
                >
                  NUTRITION CALCULATOR
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
