import { useState } from 'react'
import { Calculator, Target, TrendingUp, Info } from 'lucide-react'

const TDEECalculator = () => {
  const [formData, setFormData] = useState({
    age: '',
    gender: 'male',
    weight: '',
    height: '',
    activityLevel: '1.2',
    goal: 'maintain'
  })
  const [results, setResults] = useState(null)

  const activityLevels = {
    '1.2': 'Sedentary (little or no exercise)',
    '1.375': 'Lightly active (light exercise/sports 1-3 days/week)',
    '1.55': 'Moderately active (moderate exercise/sports 3-5 days/week)',
    '1.725': 'Very active (hard exercise/sports 6-7 days a week)',
    '1.9': 'Extra active (very hard exercise/sports & physical job)'
  }

  const goals = {
    'lose': { label: 'Lose Weight', multiplier: 0.8, color: 'text-red-600' },
    'maintain': { label: 'Maintain Weight', multiplier: 1, color: 'text-green-600' },
    'gain': { label: 'Gain Weight', multiplier: 1.2, color: 'text-blue-600' }
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const calculateTDEE = (e) => {
    e.preventDefault()
    
    const { age, gender, weight, height, activityLevel, goal } = formData
    
    // Convert inputs to numbers
    const ageNum = parseInt(age)
    const weightNum = parseFloat(weight)
    const heightNum = parseFloat(height)
    const activityNum = parseFloat(activityLevel)
    
    // Calculate BMR using Mifflin-St Jeor Equation
    let bmr
    if (gender === 'male') {
      bmr = (10 * weightNum) + (6.25 * heightNum) - (5 * ageNum) + 5
    } else {
      bmr = (10 * weightNum) + (6.25 * heightNum) - (5 * ageNum) - 161
    }
    
    // Calculate TDEE
    const tdee = bmr * activityNum
    
    // Calculate goal calories
    const goalCalories = tdee * goals[goal].multiplier
    
    // Calculate macros (example ratios)
    const protein = Math.round((goalCalories * 0.25) / 4) // 25% protein
    const carbs = Math.round((goalCalories * 0.45) / 4) // 45% carbs
    const fats = Math.round((goalCalories * 0.30) / 9) // 30% fats
    
    setResults({
      bmr: Math.round(bmr),
      tdee: Math.round(tdee),
      goalCalories: Math.round(goalCalories),
      macros: { protein, carbs, fats },
      goal: goals[goal]
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">TDEE Calculator</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Calculate your Total Daily Energy Expenditure and get personalized calorie recommendations 
            based on your fitness goals.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Calculator Form */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold mb-6 flex items-center gap-2">
              <Calculator className="w-6 h-6 text-blue-600" />
              Your Information
            </h2>
            
            <form onSubmit={calculateTDEE} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Age (years)
                  </label>
                  <input
                    type="number"
                    name="age"
                    value={formData.age}
                    onChange={handleChange}
                    required
                    min="15"
                    max="100"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="25"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Gender
                  </label>
                  <select
                    name="gender"
                    value={formData.gender}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                </div>
              </div>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Weight (kg)
                  </label>
                  <input
                    type="number"
                    name="weight"
                    value={formData.weight}
                    onChange={handleChange}
                    required
                    min="30"
                    max="300"
                    step="0.1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="70"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Height (cm)
                  </label>
                  <input
                    type="number"
                    name="height"
                    value={formData.height}
                    onChange={handleChange}
                    required
                    min="100"
                    max="250"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="175"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Activity Level
                </label>
                <select
                  name="activityLevel"
                  value={formData.activityLevel}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {Object.entries(activityLevels).map(([value, label]) => (
                    <option key={value} value={value}>{label}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Goal
                </label>
                <select
                  name="goal"
                  value={formData.goal}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {Object.entries(goals).map(([value, { label }]) => (
                    <option key={value} value={value}>{label}</option>
                  ))}
                </select>
              </div>
              
              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors font-semibold"
              >
                Calculate TDEE
              </button>
            </form>
          </div>

          {/* Results */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold mb-6 flex items-center gap-2">
              <Target className="w-6 h-6 text-green-600" />
              Your Results
            </h2>
            
            {results ? (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-sm text-blue-600 font-medium">BMR</div>
                    <div className="text-2xl font-bold text-blue-700">{results.bmr}</div>
                    <div className="text-xs text-blue-600">calories/day</div>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-sm text-green-600 font-medium">TDEE</div>
                    <div className="text-2xl font-bold text-green-700">{results.tdee}</div>
                    <div className="text-xs text-green-600">calories/day</div>
                  </div>
                </div>
                
                <div className={`bg-gray-50 p-4 rounded-lg border-l-4 border-${results.goal.color.split('-')[1]}-500`}>
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className={`w-5 h-5 ${results.goal.color}`} />
                    <span className="font-semibold">Goal: {results.goal.label}</span>
                  </div>
                  <div className="text-3xl font-bold mb-1">{results.goalCalories}</div>
                  <div className="text-sm text-gray-600">calories per day</div>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-3">Recommended Macros</h3>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="text-center p-3 bg-red-50 rounded-lg">
                      <div className="text-lg font-bold text-red-700">{results.macros.protein}g</div>
                      <div className="text-xs text-red-600">Protein</div>
                    </div>
                    <div className="text-center p-3 bg-yellow-50 rounded-lg">
                      <div className="text-lg font-bold text-yellow-700">{results.macros.carbs}g</div>
                      <div className="text-xs text-yellow-600">Carbs</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <div className="text-lg font-bold text-purple-700">{results.macros.fats}g</div>
                      <div className="text-xs text-purple-600">Fats</div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-12">
                <Calculator className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <p>Fill out the form to calculate your TDEE</p>
              </div>
            )}
          </div>
        </div>

        {/* Info Section */}
        <div className="mt-12 bg-blue-50 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <Info className="w-6 h-6 text-blue-600 mt-1" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">About TDEE</h3>
              <p className="text-blue-800 text-sm">
                Total Daily Energy Expenditure (TDEE) is the total number of calories you burn in a day. 
                It includes your Basal Metabolic Rate (BMR) plus calories burned through physical activity. 
                This calculator uses the Mifflin-St Jeor equation, which is considered one of the most accurate methods.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TDEECalculator
